<template>
  <div class="user-profile">
    <!-- 智能搜索框 -->
    <SmartSearchBox
      ref="searchBoxRef"
      v-model:keyword="keyword"
      @search="handleSearch"
    />

    <!-- 内容区域 -->
    <div class="container" :style="{ paddingTop: containerPaddingTop + 'px' }">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>

      <!-- 微博卡片 -->
      <template v-else>
        <!-- 调试信息显示 -->
        <div v-if="showDebugInfo" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px;">
          <strong>🔍 渲染调试信息:</strong><br>
          loading: {{ loading }}<br>
          cards.length: {{ cards.length }}<br>
          nextCursor: {{ nextCursor }}<br>
          loadingMore: {{ loadingMore }}<br>
          显示条件检查:<br>
          - !loading: {{ !loading }}<br>
          - cards.length === 0: {{ cards.length === 0 }}<br>
          - 应该显示空状态: {{ !loading && cards.length === 0 }}<br>
          - 应该显示卡片: {{ cards.length > 0 }}
        </div>

        <ul class="infinite-list" style="overflow:visible">
          <li
            v-for="(card, index) in cards"
            :key="card.id"
            class="card-observer infinite-list-item"
            :data-index="index"
          >
            <weibo-card
              :card="card"
              :is-visible="card.isCardVisible"
              :clickable="true"
              @card-click="handleCardClick"
            ></weibo-card>
          </li>
        </ul>

        <!-- 加载更多状态 -->
        <div v-if="loadingMore" class="loading-more">
          <el-skeleton :rows="2" animated />
        </div>

        <div v-if="!nextCursor && cards.length && !loadingMore" class="no-more">
          —— 已显示全部内容 ——
        </div>

        <!-- 空状态显示 -->
        <div v-if="!loading && cards.length === 0" class="empty-state">
          <div class="empty-content">
            <el-empty
              description="暂无微博内容"
              :image-size="120"
            >
              <template #description>
                <p>该用户还没有发布微博，或者微博内容暂时无法加载</p>
              </template>
            </el-empty>
          </div>
        </div>
      </template>
    </div>

    <!-- 返回顶部 -->
    <button class="back-to-top" @click="scrollToTop" v-show="showBackTop">
      ↑
    </button>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import WeiboCard from '../components/WeiboCard.vue'
import SmartSearchBox from '../components/SmartSearchBox.vue'
import { weiboService } from '../services/weiboService'
import { API_CONFIG, isDevelopment } from '../config/api.js'

export default {
  name: 'UserProfile',
  components: {
    WeiboCard,
    SmartSearchBox
  },
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()

    const searchBoxRef = ref(null)
    const cards = ref([])
    const loading = ref(true)
    const loadingMore = ref(false)
    const autoLoading = ref(false)
    const showDebugInfo = ref(isDevelopment())

    // 计算容器的 padding-top，与搜索框的显示状态同步
    const containerPaddingTop = computed(() => {
      if (searchBoxRef.value) {
        return searchBoxRef.value.firstCardPaddingTop || 80
      }
      return 80 // 默认搜索框高度
    })
    const nextCursor = ref(null)
    const keyword = ref('')
    const showBackTop = ref(false)
    const searchTimer = ref(null)
    const scrollListener = ref(null)
    const isRequesting = ref(false)
    const lastScrollTop = ref(0)

    const initScrollListener = () => {
      let ticking = false
      let lastLoadTime = 0
      
      scrollListener.value = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            const currentScrollTop = window.scrollY
            showBackTop.value = currentScrollTop > 300

            if (loading.value || loadingMore.value || isRequesting.value || !nextCursor.value) {
              lastScrollTop.value = currentScrollTop
              ticking = false
              return
            }

            const { scrollTop, clientHeight, scrollHeight } = document.documentElement
            const scrollPosition = scrollTop + clientHeight
            const distanceFromBottom = scrollHeight - scrollPosition
            
            const scrollDelta = currentScrollTop - lastScrollTop.value
            const isFastScrollDown = scrollDelta > 100

            const shouldLoad = (distanceFromBottom <= 500) || 
                              (distanceFromBottom <= 800 && isFastScrollDown) ||
                              (distanceFromBottom <= 10)
            
            if (shouldLoad && nextCursor.value) {
              const now = Date.now()
              let minInterval = 500
              if (distanceFromBottom <= 10) {
                minInterval = 100
              } else if (isFastScrollDown) {
                minInterval = 200
              }
              
              if (now - lastLoadTime >= minInterval) {
                lastLoadTime = now
                loadMore()
              }
            }
            
            lastScrollTop.value = currentScrollTop
            ticking = false
          })
          ticking = true
        }
      }
      
      window.addEventListener('scroll', scrollListener.value, { passive: true })
    }

    const handleSearch = () => {
      clearTimeout(searchTimer.value)
      searchTimer.value = setTimeout(() => {
        cards.value = []
        nextCursor.value = null
        isRequesting.value = false
        loadingMore.value = false
        autoLoading.value = false
        loading.value = true
        loadData()
      }, 300)
    }

    const loadData = async () => {
      console.log('🔍 loadData 函数开始执行')
      console.log('  - 当前状态: loading:', loading.value, 'loadingMore:', loadingMore.value)
      console.log('  - 当前 cards.length:', cards.value.length)

      try {
        if (!loadingMore.value) {
          loading.value = true
          console.log('🔍 设置 loading.value = true')
        }

        const params = {
          user: props.userId,
          limit: 5,
          cursor: nextCursor.value,
          keyword: keyword.value
        }

        console.log('🔍 请求参数:', params)
        console.log('🔍 开始调用 weiboService.getTweets')

        const data = await weiboService.getTweets(params)

        // 调试日志
        console.log('🔍 API 响应数据:', data)
        console.log('🔍 data?.success:', data?.success)
        console.log('🔍 data.data?.length:', data.data?.length)
        console.log('🔍 data.data:', data.data)

        if (data?.success) {
          const newCards = data.data?.length ? data.data.map(card => ({
            ...card,
            isCardVisible: false
          })) : []

          console.log('🔍 处理后的 newCards:', newCards)
          console.log('🔍 newCards.length:', newCards.length)

          if (newCards.length > 0) {
            const oldLength = cards.value.length
            cards.value = cards.value.concat(newCards)
            nextCursor.value = data.next_cursor || null
            console.log('✅ 数据添加成功:')
            console.log('  - 添加前 cards.length:', oldLength)
            console.log('  - 添加的 newCards.length:', newCards.length)
            console.log('  - 添加后 cards.value.length:', cards.value.length)
            console.log('  - cards.value 内容:', cards.value)
            console.log('  - nextCursor.value:', nextCursor.value)
          } else {
            nextCursor.value = null
            console.log('⚠️ newCards 为空，显示空状态')
            if (autoLoading.value) {
              autoLoading.value = false
              ElMessage.info('没有更多内容了')
            }
          }
        } else {
          console.log('❌ API 响应失败或 success 为 false')
          nextCursor.value = null
          autoLoading.value = false
          if (loadingMore.value) {
            ElMessage.info('没有更多内容了')
          }
        }
      } catch (error) {
        nextCursor.value = null
        autoLoading.value = false
        console.error('加载错误:', error)
        if (loadingMore.value) {
          ElMessage.error('加载失败，请重试')
        }
      } finally {
        loading.value = false
        loadingMore.value = false
        console.log('🔍 finally 块执行完毕:')
        console.log('  - loading.value:', loading.value)
        console.log('  - loadingMore.value:', loadingMore.value)
        console.log('  - cards.value.length:', cards.value.length)
        console.log('  - cards.value:', cards.value)
      }
    }

    const loadMore = async () => {
      if (!nextCursor.value || loadingMore.value || isRequesting.value) {
        return
      }
      
      isRequesting.value = true
      loadingMore.value = true
      autoLoading.value = true
      
      try {
        await loadData()
        await nextTick()
      } finally {
        isRequesting.value = false
      }
    }

    const scrollToTop = () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }

    const handleCardClick = (card) => {
      // 跳转到微博详情页
      router.push(`/detail/${card.id}`)
    }

    // 监听 cards 数组变化
    watch(cards, (newCards, oldCards) => {
      console.log('🔍 cards 数组发生变化:')
      console.log('  - 旧长度:', oldCards?.length || 0)
      console.log('  - 新长度:', newCards?.length || 0)
      console.log('  - 新内容:', newCards)
    }, { deep: true })

    // 监听 loading 状态变化
    watch(loading, (newLoading, oldLoading) => {
      console.log('🔍 loading 状态变化:', oldLoading, '->', newLoading)
    })

    // 监听路由参数变化
    watch(() => props.userId, (newUserId) => {
      if (newUserId) {
        console.log('🔍 用户ID变化，重置状态:', newUserId)
        // 重置状态
        cards.value = []
        nextCursor.value = null
        isRequesting.value = false
        loadingMore.value = false
        autoLoading.value = false
        keyword.value = ''
        loading.value = true
        loadData()
      }
    })

    onMounted(() => {
      if (isDevelopment()) {
        console.log('🌍 当前环境:', API_CONFIG.ENV)
        console.log('🔗 API 地址:', API_CONFIG.BASE_URL)
      }

      console.log('🔍 onMounted 执行，初始状态:')
      console.log('  - props.userId:', props.userId)
      console.log('  - loading.value:', loading.value)
      console.log('  - cards.value.length:', cards.value.length)

      if (props.userId) {
        console.log('✅ 初始化用户页面，用户ID:', props.userId)
        isRequesting.value = false
        loadingMore.value = false
        autoLoading.value = false
        console.log('🔍 开始调用 loadData()')
        loadData()
        initScrollListener()
      } else {
        console.log('❌ 用户ID参数错误')
        ElMessage.error('用户ID参数错误')
        loading.value = false
      }
    })

    onUnmounted(() => {
      if (scrollListener.value) {
        window.removeEventListener('scroll', scrollListener.value, { passive: true })
      }
    })

    return {
      searchBoxRef,
      containerPaddingTop,
      cards,
      loading,
      loadingMore,
      autoLoading,
      nextCursor,
      keyword,
      showBackTop,
      searchTimer,
      scrollListener,
      isRequesting,
      lastScrollTop,
      handleSearch,
      loadData,
      loadMore,
      scrollToTop,
      handleCardClick
    }
  }
}
</script>

<style lang="scss" scoped>
.user-profile {
  min-height: 100vh;
  // padding-top 现在由 JavaScript 动态控制
}



.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 $space-lg;
  
  @media (max-width: #{$breakpoint-mobile - 1px}) {
    padding: 0 $space-md;
  }
}

.loading-container {
  padding: $space-xl 0;
  
  .el-skeleton {
    @include card-base;
    padding: $space-xl;
    margin-bottom: $space-lg;
  }
}

.infinite-list {
  list-style: none;
  padding: 0;
  margin: $space-lg 0;
  
  &-item {
    margin-bottom: $space-lg;
    animation: fadeInUp 0.6s ease-out;
    
    @include mobile {
      margin-bottom: $space-md;
    }
  }
}

.loading-more {
  padding: $space-xl 0;
  text-align: center;
  
  .el-skeleton {
    @include card-base;
    padding: $space-xl;
    margin: 0 auto;
  }
}

.no-more {
  text-align: center;
  color: $text-tertiary;
  padding: $space-2xl 0;
  font-size: $font-size-sm;
  position: relative;
  
  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 60px;
    height: 1px;
    background: linear-gradient(90deg, transparent, $border-default, transparent);
  }
  
  &::before {
    left: calc(50% - 120px);
  }
  
  &::after {
    right: calc(50% - 120px);
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: $space-2xl;

  .empty-content {
    text-align: center;
    max-width: 400px;

    p {
      color: $text-secondary;
      font-size: 14px;
      margin-top: $space-md;
      line-height: 1.5;
    }
  }
}

.back-to-top {
  position: fixed;
  bottom: $space-2xl;
  right: $space-2xl;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, $primary-color 0%, $primary-hover 100%);
  color: white;
  border: none;
  cursor: pointer;
  font-size: 20px;
  font-weight: bold;
  box-shadow: $shadow-3;
  transition: all $transition-base;
  z-index: $z-index-fixed;
  @include flex-center;
  
  &:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: $shadow-hover;
  }
  
  &:active {
    transform: translateY(-2px) scale(1.02);
  }
  
  @include mobile {
    width: 48px;
    height: 48px;
    bottom: $space-lg;
    right: $space-lg;
    font-size: 18px;
  }
}

// 页面进入动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>